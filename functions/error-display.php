<?php
/**
 * Error Display Component
 * Provides smart error display for customers vs admins
 * with appropriate level of detail for each audience
 */

require_once __DIR__ . '/enhanced-error-handler.php';

class ErrorDisplay {
    
    /**
     * Display error message appropriate for the current user type
     */
    public static function show($errorData, $isAdmin = false, $operation = '') {
        if ($isAdmin) {
            return self::showAdminError($errorData, $operation);
        } else {
            return self::showCustomerError($errorData, $operation);
        }
    }
    
    /**
     * Show detailed error for admin users
     */
    private static function showAdminError($errorData, $operation = '') {
        $severity = $errorData['severity'] ?? EnhancedErrorHandler::SEVERITY_ERROR;
        $category = $errorData['category'] ?? EnhancedErrorHandler::CATEGORY_SYSTEM;
        $message = $errorData['message'] ?? 'Unknown error occurred';
        $context = $errorData['context'] ?? [];
        
        // Determine alert class based on severity
        $alertClass = self::getAlertClass($severity);
        
        $html = '<div class="alert ' . $alertClass . ' alert-dismissible fade show" role="alert">';
        $html .= '<h5><i class="fas fa-exclamation-triangle"></i> ';
        
        if ($operation) {
            $html .= ucfirst($operation) . ' Failed';
        } else {
            $html .= ucfirst($severity) . ' Error';
        }
        
        $html .= '</h5>';
        $html .= '<p><strong>Error:</strong> ' . htmlspecialchars($message) . '</p>';
        
        // Show technical details for admins
        if (!empty($context)) {
            $html .= '<hr>';
            $html .= '<h6>Technical Details:</h6>';
            
            // Show API response details if available
            if (isset($context['api_response'])) {
                $apiResponse = $context['api_response'];
                $html .= '<p><strong>API Status:</strong> ' . ($apiResponse['status'] ?? 'Unknown') . '</p>';
                if (isset($apiResponse['error'])) {
                    $html .= '<p><strong>API Error:</strong> ' . htmlspecialchars($apiResponse['error']) . '</p>';
                }
                if (isset($apiResponse['data'])) {
                    $html .= '<details><summary>API Response Data</summary>';
                    $html .= '<pre>' . htmlspecialchars(json_encode($apiResponse['data'], JSON_PRETTY_PRINT)) . '</pre>';
                    $html .= '</details>';
                }
            }
            
            // Show database error details
            if (isset($context['mysql_error'])) {
                $html .= '<p><strong>Database Error:</strong> ' . htmlspecialchars($context['mysql_error']) . '</p>';
            }
            
            if (isset($context['query'])) {
                $html .= '<p><strong>Query:</strong> <code>' . htmlspecialchars($context['query']) . '</code></p>';
            }
            
            // Show validation errors
            if (isset($context['validation_errors'])) {
                $html .= '<p><strong>Validation Errors:</strong></p>';
                $html .= '<ul>';
                foreach ($context['validation_errors'] as $error) {
                    $html .= '<li>' . htmlspecialchars($error) . '</li>';
                }
                $html .= '</ul>';
            }
            
            // Show timestamp and user info
            if (isset($errorData['timestamp'])) {
                $html .= '<p><strong>Time:</strong> ' . htmlspecialchars($errorData['timestamp']) . '</p>';
            }
            
            if (isset($errorData['user_id'])) {
                $html .= '<p><strong>User:</strong> ' . htmlspecialchars($errorData['username'] ?? 'Unknown') . ' (ID: ' . $errorData['user_id'] . ')</p>';
            }
        }
        
        // Add troubleshooting suggestions
        $html .= self::getAdminTroubleshootingTips($category, $context);
        
        $html .= '<button type="button" class="close" data-dismiss="alert" aria-label="Close">';
        $html .= '<span aria-hidden="true">&times;</span>';
        $html .= '</button>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Show user-friendly error for customers
     */
    private static function showCustomerError($errorData, $operation = '') {
        $severity = $errorData['severity'] ?? EnhancedErrorHandler::SEVERITY_ERROR;
        $category = $errorData['category'] ?? EnhancedErrorHandler::CATEGORY_SYSTEM;
        
        // Determine alert class based on severity
        $alertClass = self::getAlertClass($severity);
        
        $html = '<div class="alert ' . $alertClass . ' alert-dismissible fade show" role="alert">';
        $html .= '<h5><i class="fas fa-exclamation-circle"></i> ';
        
        // User-friendly titles
        if ($operation) {
            $html .= self::getCustomerFriendlyTitle($operation);
        } else {
            $html .= 'Something went wrong';
        }
        
        $html .= '</h5>';
        
        // User-friendly messages based on category
        $html .= '<p>' . self::getCustomerFriendlyMessage($category, $operation) . '</p>';
        
        // Add helpful suggestions
        $html .= self::getCustomerHelpSuggestions($category, $operation);
        
        $html .= '<button type="button" class="close" data-dismiss="alert" aria-label="Close">';
        $html .= '<span aria-hidden="true">&times;</span>';
        $html .= '</button>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Get Bootstrap alert class based on severity
     */
    private static function getAlertClass($severity) {
        switch ($severity) {
            case EnhancedErrorHandler::SEVERITY_INFO:
                return 'alert-info';
            case EnhancedErrorHandler::SEVERITY_WARNING:
                return 'alert-warning';
            case EnhancedErrorHandler::SEVERITY_ERROR:
                return 'alert-danger';
            case EnhancedErrorHandler::SEVERITY_CRITICAL:
                return 'alert-danger';
            default:
                return 'alert-warning';
        }
    }
    
    /**
     * Get customer-friendly title for operation
     */
    private static function getCustomerFriendlyTitle($operation) {
        $titles = [
            'ticket_creation' => 'Unable to Create Ticket',
            'customer_creation' => 'Account Setup Issue',
            'profile_update' => 'Profile Update Failed',
            'login' => 'Login Problem',
            'payment' => 'Payment Issue',
            'api_sync' => 'Service Temporarily Unavailable'
        ];
        
        return $titles[$operation] ?? 'Operation Failed';
    }
    
    /**
     * Get customer-friendly message based on category
     */
    private static function getCustomerFriendlyMessage($category, $operation) {
        switch ($category) {
            case EnhancedErrorHandler::CATEGORY_API:
                return 'Our support system is temporarily experiencing issues. Your request could not be processed at this time.';
                
            case EnhancedErrorHandler::CATEGORY_DATABASE:
                return 'We\'re experiencing technical difficulties. Please try again in a few moments.';
                
            case EnhancedErrorHandler::CATEGORY_VALIDATION:
                return 'Please check the information you entered and try again.';
                
            case EnhancedErrorHandler::CATEGORY_AUTHENTICATION:
                return 'There was a problem verifying your account. Please check your credentials and try again.';
                
            case EnhancedErrorHandler::CATEGORY_NETWORK:
                return 'We\'re having trouble connecting to our services. Please check your internet connection and try again.';
                
            default:
                return 'An unexpected error occurred. Please try again or contact our support team if the problem persists.';
        }
    }
    
    /**
     * Get helpful suggestions for customers
     */
    private static function getCustomerHelpSuggestions($category, $operation) {
        $html = '<div class="mt-3">';
        $html .= '<h6>What you can do:</h6>';
        $html .= '<ul class="mb-0">';
        
        switch ($category) {
            case EnhancedErrorHandler::CATEGORY_API:
            case EnhancedErrorHandler::CATEGORY_NETWORK:
                $html .= '<li>Wait a few minutes and try again</li>';
                $html .= '<li>Check your internet connection</li>';
                $html .= '<li>Contact our support team if the issue persists</li>';
                break;
                
            case EnhancedErrorHandler::CATEGORY_VALIDATION:
                $html .= '<li>Double-check all required fields are filled out</li>';
                $html .= '<li>Make sure your email address is valid</li>';
                $html .= '<li>Ensure passwords meet the requirements</li>';
                break;
                
            case EnhancedErrorHandler::CATEGORY_AUTHENTICATION:
                $html .= '<li>Verify your username and password</li>';
                $html .= '<li>Try resetting your password if needed</li>';
                $html .= '<li>Clear your browser cache and cookies</li>';
                break;
                
            default:
                $html .= '<li>Refresh the page and try again</li>';
                $html .= '<li>Try using a different browser</li>';
                $html .= '<li>Contact our support team for assistance</li>';
                break;
        }
        
        $html .= '</ul>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Get troubleshooting tips for admins
     */
    private static function getAdminTroubleshootingTips($category, $context) {
        $html = '<hr>';
        $html .= '<h6>Troubleshooting Tips:</h6>';
        $html .= '<ul class="mb-0">';

        switch ($category) {
            case EnhancedErrorHandler::CATEGORY_API:
                $html .= '<li>Check API endpoint availability and response times</li>';
                $html .= '<li>Verify API key is valid and has proper permissions</li>';
                $html .= '<li>Review API rate limits and quotas</li>';

                // Specific troubleshooting for customer creation errors
                if (isset($context['operation']) && $context['operation'] === 'customer_creation') {
                    $html .= '<li><strong>Customer Creation Issues:</strong></li>';
                    $html .= '<ul>';
                    $html .= '<li>Check if customer number already exists in Appika</li>';
                    $html .= '<li>Verify customer data format matches Appika requirements</li>';
                    $html .= '<li>Test customer creation directly in Appika API</li>';
                    $html .= '<li>Compare localhost vs server environment differences</li>';
                    if (isset($context['api_response']['message']) && strpos($context['api_response']['message'], 'already exist') !== false) {
                        $html .= '<li><span class="text-warning">⚠️ Customer number conflict detected - user may already exist in Appika</span></li>';
                    }
                    $html .= '</ul>';
                }

                if (isset($context['status_code'])) {
                    $statusCode = $context['status_code'];
                    if ($statusCode >= 500) {
                        $html .= '<li>Server error - contact API provider support</li>';
                    } elseif ($statusCode == 401 || $statusCode == 403) {
                        $html .= '<li>Authentication issue - check API credentials</li>';
                    } elseif ($statusCode == 429) {
                        $html .= '<li>Rate limit exceeded - implement retry logic</li>';
                    }
                }

                // Environment-specific troubleshooting
                $html .= '<li><strong>Environment Differences:</strong></li>';
                $html .= '<ul>';
                $html .= '<li>Compare API endpoints between localhost and server</li>';
                $html .= '<li>Check network connectivity and firewall rules</li>';
                $html .= '<li>Verify SSL/TLS certificate validation</li>';
                $html .= '<li>Test API calls with same data on both environments</li>';
                $html .= '</ul>';
                break;
                
            case EnhancedErrorHandler::CATEGORY_DATABASE:
                $html .= '<li>Check database connection and server status</li>';
                $html .= '<li>Verify table structure and column names</li>';
                $html .= '<li>Review database user permissions</li>';
                $html .= '<li>Check for database locks or deadlocks</li>';
                break;
                
            case EnhancedErrorHandler::CATEGORY_NETWORK:
                $html .= '<li>Test network connectivity to external services</li>';
                $html .= '<li>Check firewall and proxy settings</li>';
                $html .= '<li>Verify DNS resolution</li>';
                $html .= '<li>Review timeout settings</li>';
                break;
                
            default:
                $html .= '<li>Check server error logs for more details</li>';
                $html .= '<li>Verify system resources (memory, disk space)</li>';
                $html .= '<li>Review recent code changes</li>';
                break;
        }
        
        $html .= '</ul>';
        
        return $html;
    }
    
    /**
     * Quick error display for AJAX responses
     */
    public static function getAjaxError($errorData, $isAdmin = false) {
        $message = $errorData['message'] ?? 'Unknown error occurred';
        
        if ($isAdmin) {
            // Include technical details for admin AJAX responses
            $details = [];
            if (isset($errorData['context']['api_response']['status'])) {
                $details[] = 'Status: ' . $errorData['context']['api_response']['status'];
            }
            if (isset($errorData['context']['mysql_error'])) {
                $details[] = 'DB: ' . $errorData['context']['mysql_error'];
            }
            
            if (!empty($details)) {
                $message .= ' (' . implode(', ', $details) . ')';
            }
        } else {
            // User-friendly message for customers
            $category = $errorData['category'] ?? EnhancedErrorHandler::CATEGORY_SYSTEM;
            $message = self::getCustomerFriendlyMessage($category, '');
        }
        
        return $message;
    }
}
