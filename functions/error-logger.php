<?php
/**
 * Error Logger Utility
 * Provides convenient functions for logging different types of errors
 * with proper context and formatting
 */

require_once __DIR__ . '/enhanced-error-handler.php';

class ErrorLogger {
    
    /**
     * Log ticket creation errors
     */
    public static function logTicketError($error, $ticketData = [], $apiResponse = null) {
        $context = [
            'operation' => 'ticket_creation',
            'ticket_data' => $ticketData
        ];
        
        if ($apiResponse) {
            $context['api_response'] = $apiResponse;
            return EnhancedErrorHandler::handleApiError($apiResponse, 'ticket creation', $context);
        } else {
            return EnhancedErrorHandler::handleError($error, $context, EnhancedErrorHandler::SEVERITY_ERROR, EnhancedErrorHandler::CATEGORY_SYSTEM);
        }
    }
    
    /**
     * Log customer creation errors
     */
    public static function logCustomerError($error, $customerData = [], $apiResponse = null) {
        // Detect environment
        $isLocalhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);

        $context = [
            'operation' => 'customer_creation',
            'customer_data' => self::sanitizeCustomerData($customerData),
            'environment' => $isLocalhost ? 'localhost' : 'server',
            'host' => $_SERVER['HTTP_HOST'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];

        // Add specific context for environment differences
        if ($isLocalhost) {
            $context['environment_notes'] = [
                'This error occurred on localhost',
                'Check if same operation works on server',
                'Verify API endpoints and network connectivity',
                'Compare localhost vs server configuration'
            ];
        } else {
            $context['environment_notes'] = [
                'This error occurred on server',
                'Check if same operation works on localhost',
                'Verify server-specific configurations',
                'Check server firewall and network settings'
            ];
        }

        if ($apiResponse) {
            $context['api_response'] = $apiResponse;

            // Add specific handling for "already exists" errors
            if (isset($apiResponse['message']) && strpos($apiResponse['message'], 'already exist') !== false) {
                $context['error_type'] = 'duplicate_customer';
                $context['troubleshooting_notes'] = [
                    'Customer number already exists in Appika',
                    'Check if user was previously created',
                    'Consider using different customer number generation',
                    'Verify customer data synchronization between environments'
                ];
            }

            return EnhancedErrorHandler::handleApiError($apiResponse, 'customer creation', $context);
        } else {
            return EnhancedErrorHandler::handleError($error, $context, EnhancedErrorHandler::SEVERITY_ERROR, EnhancedErrorHandler::CATEGORY_SYSTEM);
        }
    }
    
    /**
     * Log profile update errors
     */
    public static function logProfileError($error, $profileData = [], $apiResponse = null) {
        $context = [
            'operation' => 'profile_update',
            'profile_data' => self::sanitizeCustomerData($profileData)
        ];
        
        if ($apiResponse) {
            $context['api_response'] = $apiResponse;
            return EnhancedErrorHandler::handleApiError($apiResponse, 'profile update', $context);
        } else {
            return EnhancedErrorHandler::handleError($error, $context, EnhancedErrorHandler::SEVERITY_ERROR, EnhancedErrorHandler::CATEGORY_SYSTEM);
        }
    }
    
    /**
     * Log GraphQL API errors
     */
    public static function logGraphQLError($error, $query = '', $variables = [], $response = null) {
        $context = [
            'operation' => 'graphql_request',
            'query' => $query,
            'variables' => $variables
        ];
        
        if ($response) {
            $context['graphql_response'] = $response;
            
            // Check for GraphQL-specific errors
            if (isset($response['errors']) && is_array($response['errors'])) {
                $graphqlErrors = [];
                foreach ($response['errors'] as $gqlError) {
                    $graphqlErrors[] = $gqlError['message'] ?? 'Unknown GraphQL error';
                }
                $error = 'GraphQL errors: ' . implode(', ', $graphqlErrors);
            }
        }
        
        return EnhancedErrorHandler::handleError($error, $context, EnhancedErrorHandler::SEVERITY_ERROR, EnhancedErrorHandler::CATEGORY_API);
    }
    
    /**
     * Log payment processing errors
     */
    public static function logPaymentError($error, $paymentData = [], $stripeResponse = null) {
        $context = [
            'operation' => 'payment_processing',
            'payment_data' => self::sanitizePaymentData($paymentData)
        ];
        
        if ($stripeResponse) {
            $context['stripe_response'] = $stripeResponse;
        }
        
        return EnhancedErrorHandler::handleError($error, $context, EnhancedErrorHandler::SEVERITY_CRITICAL, EnhancedErrorHandler::CATEGORY_SYSTEM);
    }
    
    /**
     * Log authentication errors
     */
    public static function logAuthError($error, $loginData = []) {
        $context = [
            'operation' => 'authentication',
            'login_data' => [
                'username' => $loginData['username'] ?? 'Unknown',
                'email' => $loginData['email'] ?? 'Unknown',
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ]
        ];
        
        return EnhancedErrorHandler::handleAuthError($error, $context);
    }
    
    /**
     * Log database operation errors
     */
    public static function logDatabaseError($error, $query = '', $additionalContext = []) {
        return EnhancedErrorHandler::handleDatabaseError($error, $query, $additionalContext);
    }
    
    /**
     * Log validation errors
     */
    public static function logValidationError($errors, $formData = [], $formType = '') {
        $context = ['form_type' => $formType];
        return EnhancedErrorHandler::handleValidationError($errors, $formData, $context);
    }
    
    /**
     * Log network/connection errors
     */
    public static function logNetworkError($error, $endpoint = '', $additionalContext = []) {
        return EnhancedErrorHandler::handleNetworkError($error, $endpoint, $additionalContext);
    }
    
    /**
     * Log admin action errors
     */
    public static function logAdminError($error, $action = '', $targetData = []) {
        $context = [
            'operation' => 'admin_action',
            'admin_action' => $action,
            'target_data' => $targetData,
            'admin_user' => $_SESSION['admin_username'] ?? 'Unknown'
        ];
        
        return EnhancedErrorHandler::handleError($error, $context, EnhancedErrorHandler::SEVERITY_WARNING, EnhancedErrorHandler::CATEGORY_SYSTEM);
    }
    
    /**
     * Sanitize customer data for logging
     */
    private static function sanitizeCustomerData($data) {
        $sensitiveFields = ['password', 'confirm_password', 'stripe_customer_id', 'payment_method'];
        $sanitized = $data;
        
        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[REDACTED]';
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize payment data for logging
     */
    private static function sanitizePaymentData($data) {
        $sensitiveFields = ['card_number', 'cvv', 'stripe_token', 'payment_method_id', 'client_secret'];
        $sanitized = $data;
        
        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[REDACTED]';
            }
        }
        
        // Partially mask email for payment logs
        if (isset($sanitized['email'])) {
            $email = $sanitized['email'];
            $parts = explode('@', $email);
            if (count($parts) == 2) {
                $username = $parts[0];
                $domain = $parts[1];
                $maskedUsername = substr($username, 0, 2) . str_repeat('*', max(0, strlen($username) - 4)) . substr($username, -2);
                $sanitized['email'] = $maskedUsername . '@' . $domain;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Create error summary for admin dashboard
     */
    public static function getErrorSummary($hours = 24) {
        $errors = EnhancedErrorHandler::getRecentErrors(null, 1000);
        $cutoffTime = time() - ($hours * 3600);
        
        $summary = [
            'total' => 0,
            'by_severity' => [
                EnhancedErrorHandler::SEVERITY_CRITICAL => 0,
                EnhancedErrorHandler::SEVERITY_ERROR => 0,
                EnhancedErrorHandler::SEVERITY_WARNING => 0,
                EnhancedErrorHandler::SEVERITY_INFO => 0
            ],
            'by_category' => [
                EnhancedErrorHandler::CATEGORY_API => 0,
                EnhancedErrorHandler::CATEGORY_DATABASE => 0,
                EnhancedErrorHandler::CATEGORY_VALIDATION => 0,
                EnhancedErrorHandler::CATEGORY_AUTHENTICATION => 0,
                EnhancedErrorHandler::CATEGORY_SYSTEM => 0,
                EnhancedErrorHandler::CATEGORY_NETWORK => 0
            ],
            'recent_critical' => []
        ];
        
        foreach ($errors as $error) {
            $errorTime = strtotime($error['timestamp']);
            if ($errorTime > $cutoffTime) {
                $summary['total']++;
                
                $severity = strtolower($error['severity']);
                if (isset($summary['by_severity'][$severity])) {
                    $summary['by_severity'][$severity]++;
                }
                
                $category = strtolower($error['category']);
                if (isset($summary['by_category'][$category])) {
                    $summary['by_category'][$category]++;
                }
                
                // Collect critical errors for immediate attention
                if ($severity === EnhancedErrorHandler::SEVERITY_CRITICAL) {
                    $summary['recent_critical'][] = [
                        'timestamp' => $error['timestamp'],
                        'message' => $error['message'],
                        'category' => $error['category']
                    ];
                }
            }
        }
        
        return $summary;
    }
    
    /**
     * Check if system is experiencing high error rates
     */
    public static function isSystemUnhealthy($errorThreshold = 10, $criticalThreshold = 3, $timeWindow = 60) {
        $summary = self::getErrorSummary($timeWindow / 60); // Convert minutes to hours
        
        return [
            'unhealthy' => $summary['total'] > $errorThreshold || $summary['by_severity'][EnhancedErrorHandler::SEVERITY_CRITICAL] > $criticalThreshold,
            'total_errors' => $summary['total'],
            'critical_errors' => $summary['by_severity'][EnhancedErrorHandler::SEVERITY_CRITICAL],
            'error_threshold' => $errorThreshold,
            'critical_threshold' => $criticalThreshold
        ];
    }
}
