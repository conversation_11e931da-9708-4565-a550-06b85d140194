<?php
/**
 * Enhanced Error Handler
 * Provides comprehensive error handling for both customers and admins
 * with appropriate error messages and detailed logging
 */

class EnhancedErrorHandler {
    
    // Error severity levels
    const SEVERITY_INFO = 'info';
    const SEVERITY_WARNING = 'warning';
    const SEVERITY_ERROR = 'error';
    const SEVERITY_CRITICAL = 'critical';
    
    // Error categories
    const CATEGORY_API = 'api';
    const CATEGORY_DATABASE = 'database';
    const CATEGORY_VALIDATION = 'validation';
    const CATEGORY_AUTHENTICATION = 'authentication';
    const CATEGORY_SYSTEM = 'system';
    const CATEGORY_NETWORK = 'network';
    
    private static $logPath = '../logs/';
    
    /**
     * Handle and log errors with context
     */
    public static function handleError($error, $context = [], $severity = self::SEVERITY_ERROR, $category = self::CATEGORY_SYSTEM) {
        $errorData = [
            'timestamp' => date('c'),
            'severity' => $severity,
            'category' => $category,
            'message' => $error,
            'context' => $context,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
            'session_id' => session_id() ?? 'No session'
        ];
        
        // Add user context if available
        if (isset($_SESSION['user_id'])) {
            $errorData['user_id'] = $_SESSION['user_id'];
            $errorData['username'] = $_SESSION['username'] ?? 'Unknown';
        }
        
        if (isset($_SESSION['admin_username'])) {
            $errorData['admin_user'] = $_SESSION['admin_username'];
        }
        
        // Log the error
        self::logError($errorData);
        
        return $errorData;
    }
    
    /**
     * Log error to appropriate log file
     */
    private static function logError($errorData) {
        // Ensure logs directory exists
        if (!file_exists(self::$logPath)) {
            mkdir(self::$logPath, 0755, true);
        }
        
        // Determine log file based on category
        $logFile = self::$logPath . $errorData['category'] . '_errors.log';
        
        // Format log entry
        $logEntry = sprintf(
            "[%s] [%s] [%s] %s\nContext: %s\nUser: %s\nRequest: %s %s\nSession: %s\n%s\n",
            $errorData['timestamp'],
            strtoupper($errorData['severity']),
            strtoupper($errorData['category']),
            $errorData['message'],
            json_encode($errorData['context'], JSON_PRETTY_PRINT),
            ($errorData['user_id'] ?? 'Guest') . ' (' . ($errorData['username'] ?? 'N/A') . ')',
            $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
            $errorData['request_uri'],
            $errorData['session_id'],
            str_repeat('-', 80)
        );
        
        // Write to log file
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Also log critical errors to main error log
        if ($errorData['severity'] === self::SEVERITY_CRITICAL) {
            error_log("CRITICAL ERROR: " . $errorData['message'] . " | Context: " . json_encode($errorData['context']));
        }
    }
    
    /**
     * Handle API errors specifically
     */
    public static function handleApiError($apiResponse, $operation, $additionalContext = []) {
        $context = array_merge([
            'operation' => $operation,
            'api_response' => $apiResponse
        ], $additionalContext);
        
        // Determine error message and severity
        $errorMessage = "API operation failed: $operation";
        $severity = self::SEVERITY_ERROR;
        
        if (isset($apiResponse['status'])) {
            $statusCode = $apiResponse['status'];
            $context['status_code'] = $statusCode;
            
            if ($statusCode >= 500) {
                $severity = self::SEVERITY_CRITICAL;
                $errorMessage = "API server error during $operation (HTTP $statusCode)";
            } elseif ($statusCode >= 400) {
                $severity = self::SEVERITY_ERROR;
                $errorMessage = "API client error during $operation (HTTP $statusCode)";
            }
        }
        
        if (isset($apiResponse['error'])) {
            $errorMessage .= ": " . $apiResponse['error'];
        }
        
        return self::handleError($errorMessage, $context, $severity, self::CATEGORY_API);
    }
    
    /**
     * Handle database errors
     */
    public static function handleDatabaseError($error, $query = '', $additionalContext = []) {
        $context = array_merge([
            'query' => $query,
            'mysql_error' => $error
        ], $additionalContext);
        
        $errorMessage = "Database operation failed: $error";
        
        return self::handleError($errorMessage, $context, self::SEVERITY_ERROR, self::CATEGORY_DATABASE);
    }
    
    /**
     * Handle validation errors
     */
    public static function handleValidationError($errors, $formData = [], $additionalContext = []) {
        $context = array_merge([
            'validation_errors' => $errors,
            'form_data' => self::sanitizeFormData($formData)
        ], $additionalContext);
        
        $errorMessage = "Validation failed: " . implode(', ', $errors);
        
        return self::handleError($errorMessage, $context, self::SEVERITY_WARNING, self::CATEGORY_VALIDATION);
    }
    
    /**
     * Sanitize form data for logging (remove sensitive information)
     */
    private static function sanitizeFormData($formData) {
        $sensitiveFields = ['password', 'confirm_password', 'api_key', 'token'];
        $sanitized = $formData;

        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[REDACTED]';
            }
        }

        return $sanitized;
    }

    /**
     * Handle network/connection errors
     */
    public static function handleNetworkError($error, $endpoint = '', $additionalContext = []) {
        $context = array_merge([
            'endpoint' => $endpoint,
            'network_error' => $error
        ], $additionalContext);

        $errorMessage = "Network connection failed";
        if ($endpoint) {
            $errorMessage .= " to $endpoint";
        }
        $errorMessage .= ": $error";

        return self::handleError($errorMessage, $context, self::SEVERITY_CRITICAL, self::CATEGORY_NETWORK);
    }

    /**
     * Handle authentication errors
     */
    public static function handleAuthError($error, $additionalContext = []) {
        $context = array_merge([
            'auth_error' => $error
        ], $additionalContext);

        $errorMessage = "Authentication failed: $error";

        return self::handleError($errorMessage, $context, self::SEVERITY_WARNING, self::CATEGORY_AUTHENTICATION);
    }

    /**
     * Get recent errors for admin dashboard
     */
    public static function getRecentErrors($category = null, $limit = 50) {
        $errors = [];
        $logFiles = [];

        if ($category) {
            $logFiles[] = self::$logPath . $category . '_errors.log';
        } else {
            // Get all error log files
            $categories = [self::CATEGORY_API, self::CATEGORY_DATABASE, self::CATEGORY_VALIDATION,
                          self::CATEGORY_AUTHENTICATION, self::CATEGORY_SYSTEM, self::CATEGORY_NETWORK];
            foreach ($categories as $cat) {
                $logFile = self::$logPath . $cat . '_errors.log';
                if (file_exists($logFile)) {
                    $logFiles[] = $logFile;
                }
            }
        }

        foreach ($logFiles as $logFile) {
            if (file_exists($logFile)) {
                $content = file_get_contents($logFile);
                // Split by the separator line
                $errorBlocks = explode("--------------------------------------------------------------------------------", $content);

                foreach ($errorBlocks as $block) {
                    $block = trim($block);
                    if (empty($block)) continue;

                    $lines = explode("\n", $block);
                    $firstLine = trim($lines[0]);

                    // Parse the main error line
                    if (preg_match('/^\[([^\]]+)\] \[([^\]]+)\] \[([^\]]+)\] (.+)$/', $firstLine, $matches)) {
                        $error = [
                            'timestamp' => $matches[1],
                            'severity' => $matches[2],
                            'category' => $matches[3],
                            'message' => $matches[4],
                            'details' => [],
                            'context' => null,
                            'user_info' => null,
                            'request_info' => null
                        ];

                        // Parse the rest of the block
                        $contextLines = [];
                        $inContext = false;

                        for ($i = 1; $i < count($lines); $i++) {
                            $line = trim($lines[$i]);
                            if (empty($line)) continue;

                            if (strpos($line, 'Context:') === 0) {
                                $inContext = true;
                                continue;
                            } elseif (strpos($line, 'User:') === 0) {
                                $inContext = false;
                                $error['user_info'] = $line;
                                $error['details'][] = $line;
                            } elseif (strpos($line, 'Request:') === 0) {
                                $inContext = false;
                                $error['request_info'] = $line;
                                $error['details'][] = $line;
                            } elseif (strpos($line, 'Session:') === 0) {
                                $inContext = false;
                                $error['details'][] = $line;
                            } elseif ($inContext) {
                                $contextLines[] = $line;
                            }
                        }

                        // Parse JSON context if available
                        if (!empty($contextLines)) {
                            $contextJson = implode("\n", $contextLines);
                            $contextData = json_decode($contextJson, true);
                            if ($contextData) {
                                $error['context'] = $contextData;
                                $error['details'][] = "Context: " . json_encode($contextData, JSON_PRETTY_PRINT);
                            } else {
                                $error['details'][] = "Context: " . $contextJson;
                            }
                        }

                        $errors[] = $error;
                    }
                }
            }
        }

        // Sort by timestamp (most recent first)
        usort($errors, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });

        return array_slice($errors, 0, $limit);
    }

    /**
     * Clear old error logs (older than specified days)
     */
    public static function clearOldLogs($daysToKeep = 30) {
        $categories = [self::CATEGORY_API, self::CATEGORY_DATABASE, self::CATEGORY_VALIDATION,
                      self::CATEGORY_AUTHENTICATION, self::CATEGORY_SYSTEM, self::CATEGORY_NETWORK];

        foreach ($categories as $category) {
            $logFile = self::$logPath . $category . '_errors.log';
            if (file_exists($logFile)) {
                $cutoffTime = time() - ($daysToKeep * 24 * 60 * 60);
                $lines = file($logFile, FILE_IGNORE_NEW_LINES);
                $newLines = [];

                foreach ($lines as $line) {
                    if (preg_match('/^\[([^\]]+)\]/', $line, $matches)) {
                        $lineTime = strtotime($matches[1]);
                        if ($lineTime > $cutoffTime) {
                            $newLines[] = $line;
                        }
                    } else {
                        // Keep context lines if the main line was kept
                        if (!empty($newLines)) {
                            $newLines[] = $line;
                        }
                    }
                }

                file_put_contents($logFile, implode("\n", $newLines) . "\n");
            }
        }
    }
}
