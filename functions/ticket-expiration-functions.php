<?php
/**
 * Ticket Expiration Functions
 * Core functions for managing ticket expiration system
 */

require_once('../config/ticket-expiration-config.php');

/**
 * Get user's tickets ordered by purchase date (FIFO - oldest first)
 * @param string $username Username
 * @param string $ticket_type Ticket type (starter, premium, ultimate)
 * @return array Array of ticket records
 */
function getUserTicketsFIFO($username, $ticket_type = null) {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }
    
    $query = "SELECT * FROM purchasetickets 
              WHERE username = ? 
              AND remaining_tickets > 0";
    
    $params = [$username];
    $types = 's';
    
    if ($ticket_type) {
        $query .= " AND ticket_type = ?";
        $params[] = $ticket_type;
        $types .= 's';
    }
    
    $query .= " ORDER BY purchase_time ASC"; // FIFO - oldest first
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $tickets = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $tickets[] = $row;
    }
    
    mysqli_stmt_close($stmt);
    return $tickets;
}

/**
 * Use tickets with FIFO logic (oldest first)
 * @param string $username Username
 * @param string $ticket_type Ticket type
 * @param int $quantity Quantity to use
 * @return bool Success status
 */
function useTicketsFIFO($username, $ticket_type, $quantity = 1) {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }

    // Handle business/premium ticket type mapping
    // If looking for "premium" tickets, also check for "business" tickets
    $search_types = [$ticket_type];
    if ($ticket_type === 'premium') {
        $search_types = ['premium', 'business'];
    }

    $tickets = [];
    foreach ($search_types as $search_type) {
        $type_tickets = getUserTicketsFIFO($username, $search_type);
        $tickets = array_merge($tickets, $type_tickets);
    }

    // Sort by purchase_time to maintain FIFO order
    usort($tickets, function($a, $b) {
        return strtotime($a['purchase_time']) - strtotime($b['purchase_time']);
    });

    $remaining_to_use = $quantity;
    
    foreach ($tickets as $ticket) {
        if ($remaining_to_use <= 0) break;
        
        $available = $ticket['remaining_tickets'];
        $to_use = min($remaining_to_use, $available);
        
        // Update the ticket record using unique combination of fields
        $new_remaining = $available - $to_use;
        $update_query = "UPDATE purchasetickets
                        SET remaining_tickets = ?
                        WHERE username = ?
                        AND ticket_type = ?
                        AND purchase_time = ?
                        AND transactionid = ?";

        $stmt = mysqli_prepare($conn, $update_query);
        mysqli_stmt_bind_param($stmt, 'issss',
            $new_remaining,
            $ticket['username'],
            $ticket['ticket_type'],
            $ticket['purchase_time'],
            $ticket['transactionid']
        );
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        
        $remaining_to_use -= $to_use;
        
        // Log the usage
        logTicketUsage($username, $ticket_type, $to_use, $ticket['transactionid']);
    }
    
    // After using tickets, sync the user table with actual remaining tickets
    // Re-enabled to ensure user table reflects actual ticket consumption
    syncUserTableTickets($username);

    return $remaining_to_use == 0; // Return true if all tickets were used successfully
}

/**
 * Sync user table ticket counts with actual remaining tickets from purchasetickets table
 * @param string $username Username to sync
 * @return bool Success status
 */
function syncUserTableTickets($username) {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }

    // First, get expired tickets data BEFORE updating them
    $expired_tickets_query = "SELECT username, ticket_type, remaining_tickets, purchase_time, expiration_date, transactionid
                             FROM purchasetickets
                             WHERE username = ?
                             AND remaining_tickets > 0
                             AND expiration_date <= NOW()";

    $stmt = mysqli_prepare($conn, $expired_tickets_query);
    mysqli_stmt_bind_param($stmt, 's', $username);
    mysqli_stmt_execute($stmt);
    $expired_result = mysqli_stmt_get_result($stmt);

    $expired_tickets_data = [];
    while ($row = mysqli_fetch_assoc($expired_result)) {
        $expired_tickets_data[] = $row;
    }
    mysqli_stmt_close($stmt);

    // Now update the expired tickets
    if (!empty($expired_tickets_data)) {
        $expire_query = "UPDATE purchasetickets
                         SET remaining_tickets = 0,
                             expired_at = NOW()
                         WHERE username = ?
                         AND remaining_tickets > 0
                         AND expiration_date <= NOW()";

        $stmt = mysqli_prepare($conn, $expire_query);
        mysqli_stmt_bind_param($stmt, 's', $username);
        mysqli_stmt_execute($stmt);
        $expired_count = mysqli_stmt_affected_rows($stmt);
        mysqli_stmt_close($stmt);

        // Log each expired ticket with the original remaining_tickets quantity
        foreach ($expired_tickets_data as $ticket) {
            $log_query = "INSERT INTO ticket_expiration_log
                         (username, ticket_type, expired_quantity, purchase_date, expiration_date, transaction_id, cleanup_date)
                         VALUES (?, ?, ?, ?, ?, ?, NOW())";

            $stmt = mysqli_prepare($conn, $log_query);
            mysqli_stmt_bind_param($stmt, 'ssisss',
                $ticket['username'],
                $ticket['ticket_type'],
                $ticket['remaining_tickets'],
                $ticket['purchase_time'],
                $ticket['expiration_date'],
                $ticket['transactionid']
            );
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
    }

    // Get actual remaining tickets from purchasetickets table (only non-expired)
    $query = "SELECT
                ticket_type,
                SUM(remaining_tickets) as total_remaining
              FROM purchasetickets
              WHERE username = ?
              AND remaining_tickets > 0
              AND expiration_date > NOW()
              GROUP BY ticket_type";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 's', $username);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    // Initialize ticket counts
    $starter_tickets = 0;
    $premium_tickets = 0;
    $ultimate_tickets = 0;

    // Sum up tickets by type
    while ($row = mysqli_fetch_assoc($result)) {
        $ticket_type = strtolower($row['ticket_type']);
        $total = (int)$row['total_remaining'];

        switch($ticket_type) {
            case 'starter':
                $starter_tickets = $total;
                break;
            case 'business':
            case 'premium':
                $premium_tickets += $total;
                break;
            case 'ultimate':
                $ultimate_tickets = $total;
                break;
        }
    }
    mysqli_stmt_close($stmt);

    // Update user table with actual counts
    $update_query = "UPDATE user
                     SET starter_tickets = ?,
                         premium_tickets = ?,
                         ultimate_tickets = ?
                     WHERE username = ?";

    $stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($stmt, 'iiis', $starter_tickets, $premium_tickets, $ultimate_tickets, $username);
    $success = mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);

    return $success;
}

/**
 * Log ticket usage
 * @param string $username Username
 * @param string $ticket_type Ticket type
 * @param int $quantity Quantity used
 * @param string $transaction_id Transaction ID
 */
function logTicketUsage($username, $ticket_type, $quantity, $transaction_id) {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }

    $query = "INSERT INTO ticket_usage_log
              (username, ticket_type, quantity_used, transaction_id, used_at)
              VALUES (?, ?, ?, ?, NOW())";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ssis', $username, $ticket_type, $quantity, $transaction_id);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
}

/**
 * Get expired tickets for a user
 * @param string $username Username (optional)
 * @return array Array of expired ticket records
 */
function getExpiredTickets($username = null) {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }
    
    $query = "SELECT * FROM purchasetickets 
              WHERE remaining_tickets > 0 
              AND expiration_date < NOW()";
    
    $params = [];
    $types = '';
    
    if ($username) {
        $query .= " AND username = ?";
        $params[] = $username;
        $types .= 's';
    }
    
    $query .= " ORDER BY username, ticket_type, purchase_time";
    
    if ($params) {
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, $types, ...$params);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
    } else {
        $result = mysqli_query($conn, $query);
    }
    
    $expired_tickets = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $expired_tickets[] = $row;
    }
    
    if (isset($stmt)) {
        mysqli_stmt_close($stmt);
    }
    
    return $expired_tickets;
}

/**
 * Remove expired tickets and log the action
 * @param string $username Username (optional, if null removes for all users)
 * @return array Summary of removed tickets
 */
function removeExpiredTickets($username = null) {
    // COMPLETELY DISABLED FOR DEBUGGING - Always return empty array without doing anything
    return [];

    // Original function disabled:
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }
    
    $expired_tickets = getExpiredTickets($username);
    $removal_summary = [];
    
    foreach ($expired_tickets as $ticket) {
        // Log the expiration before removing
        $log_query = "INSERT INTO ticket_expiration_log 
                     (username, ticket_type, expired_quantity, purchase_date, expiration_date, transaction_id) 
                     VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = mysqli_prepare($conn, $log_query);
        mysqli_stmt_bind_param($stmt, 'ssisss', 
            $ticket['username'], 
            $ticket['ticket_type'], 
            $ticket['remaining_tickets'], 
            $ticket['purchase_time'], 
            $ticket['expiration_date'], 
            $ticket['transactionid']
        );
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
        
        // Update summary
        $key = $ticket['username'] . '_' . $ticket['ticket_type'];
        if (!isset($removal_summary[$key])) {
            $removal_summary[$key] = [
                'username' => $ticket['username'],
                'ticket_type' => $ticket['ticket_type'],
                'total_expired' => 0
            ];
        }
        $removal_summary[$key]['total_expired'] += $ticket['remaining_tickets'];
        
        // Set remaining tickets to 0 (mark as expired)
        $update_query = "UPDATE purchasetickets
                        SET remaining_tickets = 0,
                            expired_at = NOW()
                        WHERE username = ?
                        AND ticket_type = ?
                        AND purchase_time = ?
                        AND transactionid = ?";

        $stmt = mysqli_prepare($conn, $update_query);
        mysqli_stmt_bind_param($stmt, 'ssss',
            $ticket['username'],
            $ticket['ticket_type'],
            $ticket['purchase_time'],
            $ticket['transactionid']
        );
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    }

    // Update user table ticket counts based on expired tickets
    foreach ($removal_summary as $summary) {
        $username = $summary['username'];
        $ticket_type = $summary['ticket_type'];
        $expired_count = $summary['total_expired'];

        // Map ticket types to user table columns
        $column_map = [
            'starter' => 'starter_tickets',
            'business' => 'premium_tickets',
            'premium' => 'premium_tickets',
            'ultimate' => 'ultimate_tickets'
        ];

        $column = $column_map[strtolower($ticket_type)] ?? null;
        if ($column) {
            // Reduce user's ticket count (but don't go below 0)
            $update_user_query = "UPDATE user
                                 SET $column = GREATEST(0, $column - ?)
                                 WHERE username = ?";

            $stmt = mysqli_prepare($conn, $update_user_query);
            mysqli_stmt_bind_param($stmt, 'is', $expired_count, $username);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_close($stmt);
        }
    }

    // Update last cleanup timestamp
    updateTicketExpirationConfig('last_cleanup_timestamp', time());

    return array_values($removal_summary);
}

/**
 * Get tickets expiring soon
 * @param string $username Username (optional)
 * @param int $days_ahead Days to look ahead (default from config)
 * @return array Array of tickets expiring soon
 */
function getTicketsExpiringSoon($username = null, $days_ahead = null) {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }

    if ($days_ahead === null) {
        $config = getTicketExpirationConfig();
        $warning_value = $config['warning_period_days']; // Misleading name - can be any unit
        $warning_unit = $config['warning_period_unit'];

        // Build the correct SQL interval based on unit
        switch($warning_unit) {
            case 'minutes':
                $interval_sql = "INTERVAL ? MINUTE";
                break;
            case 'hours':
                $interval_sql = "INTERVAL ? HOUR";
                break;
            case 'days':
                $interval_sql = "INTERVAL ? DAY";
                break;
            case 'months':
                $interval_sql = "INTERVAL ? MONTH";
                break;
            default:
                $interval_sql = "INTERVAL ? DAY";
        }
    } else {
        $warning_value = $days_ahead;
        $interval_sql = "INTERVAL ? DAY"; // Default to days for backward compatibility
    }

    $query = "SELECT * FROM purchasetickets
              WHERE remaining_tickets > 0
              AND expiration_date > NOW()
              AND expiration_date <= DATE_ADD(NOW(), $interval_sql)";

    $params = [$warning_value];
    $types = 'i';
    
    if ($username) {
        $query .= " AND username = ?";
        $params[] = $username;
        $types .= 's';
    }
    
    $query .= " ORDER BY expiration_date ASC";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $expiring_tickets = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $expiring_tickets[] = $row;
    }
    
    mysqli_stmt_close($stmt);
    return $expiring_tickets;
}

/**
 * Get user's ticket summary with expiration info
 * @param string $username Username
 * @return array Ticket summary
 */
function getUserTicketSummaryWithExpiration($username) {
    global $conn;
    if (!$conn) {
        include_once('../functions/server.php');
    }
    
    $query = "SELECT 
                ticket_type,
                SUM(remaining_tickets) as total_remaining,
                COUNT(*) as purchase_count,
                MIN(expiration_date) as earliest_expiration,
                MAX(expiration_date) as latest_expiration
              FROM purchasetickets 
              WHERE username = ? 
              AND remaining_tickets > 0 
              AND expiration_date > NOW()
              GROUP BY ticket_type";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 's', $username);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $summary = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $summary[$row['ticket_type']] = $row;
    }
    
    mysqli_stmt_close($stmt);
    return $summary;
}

/**
 * Check if cleanup is needed based on frequency settings
 * @return bool True if cleanup is needed
 */
function isCleanupNeeded() {
    // DISABLED FOR DEBUGGING - Always return false
    return false;

    // Original code commented out:
    // $config = getTicketExpirationConfig();
    // if (!$config['auto_cleanup_enabled']) {
    //     return false;
    // }

    $last_cleanup = $config['last_cleanup_timestamp'];
    if (!$last_cleanup) {
        return true; // Never cleaned up before
    }

    // Calculate frequency in seconds based on unit
    $frequency_value = $config['cleanup_frequency_days']; // Misleading name - can be any unit
    $frequency_unit = $config['cleanup_frequency_unit'];

    switch($frequency_unit) {
        case 'minutes':
            $frequency_seconds = $frequency_value * 60;
            break;
        case 'hours':
            $frequency_seconds = $frequency_value * 60 * 60;
            break;
        case 'days':
            $frequency_seconds = $frequency_value * 24 * 60 * 60;
            break;
        case 'months':
            $frequency_seconds = $frequency_value * 30 * 24 * 60 * 60; // Approximate
            break;
        default:
            $frequency_seconds = $frequency_value * 24 * 60 * 60; // Default to days
    }

    return (time() - $last_cleanup) >= $frequency_seconds;
}

/**
 * Run automatic cleanup if needed
 * @return array|null Cleanup summary or null if not needed
 */
function runAutomaticCleanup() {
    // COMPLETELY DISABLED FOR DEBUGGING - Always return null without doing anything
    return null;

    // Original function disabled:
    // if (!isCleanupNeeded()) {
    //     return null;
    // }
    // return removeExpiredTickets();
}
?>
