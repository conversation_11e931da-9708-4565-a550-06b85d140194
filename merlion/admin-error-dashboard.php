<?php
session_start();

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

// Use absolute paths
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');

// Add enhanced error handling
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/enhanced-error-handler.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/error-display.php';
require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/error-logger.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

// Handle clear logs action
if (isset($_POST['clear_logs'])) {
    $daysToKeep = intval($_POST['days_to_keep'] ?? 7);
    EnhancedErrorHandler::clearOldLogs($daysToKeep);
    $message = "Error logs older than $daysToKeep days have been cleared.";
    $message_type = 'success';
}

// Get error summary and recent errors
$errorSummary = ErrorLogger::getErrorSummary(24); // Last 24 hours
$systemHealth = ErrorLogger::isSystemUnhealthy();
$recentErrors = EnhancedErrorHandler::getRecentErrors(null, 100);

// Filter by category if requested
$selectedCategory = $_GET['category'] ?? 'all';
if ($selectedCategory !== 'all') {
    $recentErrors = array_filter($recentErrors, function($error) use ($selectedCategory) {
        return strtolower($error['category']) === strtolower($selectedCategory);
    });
}

// Filter by severity if requested
$selectedSeverity = $_GET['severity'] ?? 'all';
if ($selectedSeverity !== 'all') {
    $recentErrors = array_filter($recentErrors, function($error) use ($selectedSeverity) {
        return strtolower($error['severity']) === strtolower($selectedSeverity);
    });
}

// Pagination
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$itemsPerPage = 20;
$totalErrors = count($recentErrors);
$totalPages = ceil($totalErrors / $itemsPerPage);
$offset = ($page - 1) * $itemsPerPage;
$paginatedErrors = array_slice($recentErrors, $offset, $itemsPerPage);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Dashboard - Admin Panel</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .error-card {
            border-left: 4px solid #dc3545;
            margin-bottom: 15px;
        }
        .error-card.warning {
            border-left-color: #ffc107;
        }
        .error-card.info {
            border-left-color: #17a2b8;
        }
        .error-card.critical {
            border-left-color: #6f42c1;
        }
        .system-health.healthy {
            color: #28a745;
        }
        .system-health.unhealthy {
            color: #dc3545;
        }
        .error-details {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .category-badge {
            font-size: 0.8em;
            padding: 2px 8px;
        }
        .severity-badge {
            font-size: 0.8em;
            padding: 2px 8px;
        }
        .severity-critical {
            background-color: #6f42c1;
            color: white;
        }
        .severity-error {
            background-color: #dc3545;
            color: white;
        }
        .severity-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .severity-info {
            background-color: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <?php include('header.php'); ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <?php include('admin-menu.php'); ?>
            </div>
            
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-exclamation-triangle"></i> Error Dashboard</h2>
                    <div>
                        <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#clearLogsModal">
                            <i class="fas fa-trash"></i> Clear Old Logs
                        </button>
                        <a href="admin-error-dashboard.php" class="btn btn-primary">
                            <i class="fas fa-sync"></i> Refresh
                        </a>
                    </div>
                </div>

                <!-- Display messages -->
                <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?php endif; ?>

                <!-- System Health Overview -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-heartbeat"></i> System Health (Last 24 Hours)</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="system-health <?php echo $systemHealth['unhealthy'] ? 'unhealthy' : 'healthy'; ?>">
                                                <i class="fas fa-<?php echo $systemHealth['unhealthy'] ? 'exclamation-triangle' : 'check-circle'; ?>"></i>
                                                <?php echo $systemHealth['unhealthy'] ? 'Unhealthy' : 'Healthy'; ?>
                                            </h3>
                                            <small>System Status</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3><?php echo $errorSummary['total']; ?></h3>
                                            <small>Total Errors</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-danger"><?php echo $errorSummary['by_severity']['critical']; ?></h3>
                                            <small>Critical Errors</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h3 class="text-warning"><?php echo $errorSummary['by_severity']['error']; ?></h3>
                                            <small>Error Level</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error Categories Summary -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie"></i> Error Categories</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($errorSummary['by_category'] as $category => $count): ?>
                                    <div class="col-md-2">
                                        <div class="text-center">
                                            <h4><?php echo $count; ?></h4>
                                            <small>
                                                <a href="?category=<?php echo $category; ?>" class="text-decoration-none">
                                                    <?php echo ucfirst($category); ?>
                                                </a>
                                            </small>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <form method="GET" class="form-inline">
                                    <div class="form-group mr-3">
                                        <label for="category" class="mr-2">Category:</label>
                                        <select name="category" id="category" class="form-control">
                                            <option value="all" <?php echo $selectedCategory === 'all' ? 'selected' : ''; ?>>All Categories</option>
                                            <option value="api" <?php echo $selectedCategory === 'api' ? 'selected' : ''; ?>>API</option>
                                            <option value="database" <?php echo $selectedCategory === 'database' ? 'selected' : ''; ?>>Database</option>
                                            <option value="validation" <?php echo $selectedCategory === 'validation' ? 'selected' : ''; ?>>Validation</option>
                                            <option value="authentication" <?php echo $selectedCategory === 'authentication' ? 'selected' : ''; ?>>Authentication</option>
                                            <option value="system" <?php echo $selectedCategory === 'system' ? 'selected' : ''; ?>>System</option>
                                            <option value="network" <?php echo $selectedCategory === 'network' ? 'selected' : ''; ?>>Network</option>
                                        </select>
                                    </div>
                                    <div class="form-group mr-3">
                                        <label for="severity" class="mr-2">Severity:</label>
                                        <select name="severity" id="severity" class="form-control">
                                            <option value="all" <?php echo $selectedSeverity === 'all' ? 'selected' : ''; ?>>All Severities</option>
                                            <option value="critical" <?php echo $selectedSeverity === 'critical' ? 'selected' : ''; ?>>Critical</option>
                                            <option value="error" <?php echo $selectedSeverity === 'error' ? 'selected' : ''; ?>>Error</option>
                                            <option value="warning" <?php echo $selectedSeverity === 'warning' ? 'selected' : ''; ?>>Warning</option>
                                            <option value="info" <?php echo $selectedSeverity === 'info' ? 'selected' : ''; ?>>Info</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Filter</button>
                                    <a href="admin-error-dashboard.php" class="btn btn-secondary ml-2">Clear Filters</a>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Errors -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Recent Errors (<?php echo $totalErrors; ?> total)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($paginatedErrors)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 48px;"></i>
                            <h4 class="mt-3">No Errors Found</h4>
                            <p class="text-muted">Great! No errors match your current filters.</p>
                        </div>
                        <?php else: ?>
                            <?php foreach ($paginatedErrors as $error): ?>
                            <div class="error-card card <?php echo strtolower($error['severity']); ?>">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="card-title">
                                                <span class="badge category-badge badge-secondary"><?php echo strtoupper($error['category']); ?></span>
                                                <span class="badge severity-badge severity-<?php echo strtolower($error['severity']); ?>"><?php echo strtoupper($error['severity']); ?></span>
                                                <?php echo htmlspecialchars($error['message']); ?>
                                            </h6>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-clock"></i> <?php echo date('M j, Y g:i A', strtotime($error['timestamp'])); ?>
                                                </small>
                                            </p>
                                            <?php if (!empty($error['details']) || !empty($error['context'])): ?>
                                            <div class="error-details">
                                                <?php if (!empty($error['context'])): ?>
                                                    <h6><i class="fas fa-info-circle"></i> Error Context:</h6>

                                                    <?php if (isset($error['context']['operation'])): ?>
                                                    <p><strong>Operation:</strong> <?php echo htmlspecialchars($error['context']['operation']); ?></p>
                                                    <?php endif; ?>

                                                    <?php if (isset($error['context']['api_response'])): ?>
                                                    <div class="mb-2">
                                                        <strong>API Response:</strong>
                                                        <div class="ml-3">
                                                            <?php if (isset($error['context']['api_response']['status'])): ?>
                                                            <p><strong>Status:</strong> <span class="badge badge-<?php echo $error['context']['api_response']['status'] >= 500 ? 'danger' : ($error['context']['api_response']['status'] >= 400 ? 'warning' : 'info'); ?>"><?php echo $error['context']['api_response']['status']; ?></span></p>
                                                            <?php endif; ?>

                                                            <?php if (isset($error['context']['api_response']['message'])): ?>
                                                            <p><strong>Message:</strong> <?php echo htmlspecialchars($error['context']['api_response']['message']); ?></p>
                                                            <?php endif; ?>

                                                            <?php if (isset($error['context']['api_response']['error'])): ?>
                                                            <p><strong>Error:</strong> <?php echo htmlspecialchars($error['context']['api_response']['error']); ?></p>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if (isset($error['context']['customer_data'])): ?>
                                                    <div class="mb-2">
                                                        <strong>Customer Data:</strong>
                                                        <div class="ml-3">
                                                            <?php foreach ($error['context']['customer_data'] as $key => $value): ?>
                                                                <?php if ($key !== 'password' && $key !== 'api_key'): ?>
                                                                <p><strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:</strong> <?php echo htmlspecialchars($value); ?></p>
                                                                <?php endif; ?>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if (isset($error['context']['ticket_data'])): ?>
                                                    <div class="mb-2">
                                                        <strong>Ticket Data:</strong>
                                                        <div class="ml-3">
                                                            <?php foreach ($error['context']['ticket_data'] as $key => $value): ?>
                                                            <p><strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:</strong> <?php echo htmlspecialchars($value); ?></p>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if (isset($error['context']['mysql_error'])): ?>
                                                    <p><strong>Database Error:</strong> <code><?php echo htmlspecialchars($error['context']['mysql_error']); ?></code></p>
                                                    <?php endif; ?>

                                                    <?php if (isset($error['context']['query'])): ?>
                                                    <p><strong>SQL Query:</strong> <code><?php echo htmlspecialchars($error['context']['query']); ?></code></p>
                                                    <?php endif; ?>

                                                    <?php if (isset($error['context']['endpoint'])): ?>
                                                    <p><strong>Endpoint:</strong> <?php echo htmlspecialchars($error['context']['endpoint']); ?></p>
                                                    <?php endif; ?>

                                                    <?php if (isset($error['context']['environment'])): ?>
                                                    <div class="mb-2">
                                                        <strong>Environment Info:</strong>
                                                        <div class="ml-3">
                                                            <p><strong>Environment:</strong>
                                                                <span class="badge badge-<?php echo $error['context']['environment'] === 'localhost' ? 'warning' : 'info'; ?>">
                                                                    <?php echo strtoupper($error['context']['environment']); ?>
                                                                </span>
                                                            </p>
                                                            <?php if (isset($error['context']['host'])): ?>
                                                            <p><strong>Host:</strong> <?php echo htmlspecialchars($error['context']['host']); ?></p>
                                                            <?php endif; ?>

                                                            <?php if (isset($error['context']['environment_notes'])): ?>
                                                            <div class="alert alert-info alert-sm">
                                                                <strong>Environment Notes:</strong>
                                                                <ul class="mb-0 mt-1">
                                                                    <?php foreach ($error['context']['environment_notes'] as $note): ?>
                                                                    <li><?php echo htmlspecialchars($note); ?></li>
                                                                    <?php endforeach; ?>
                                                                </ul>
                                                            </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if (isset($error['context']['error_type'])): ?>
                                                    <div class="mb-2">
                                                        <strong>Error Type:</strong>
                                                        <span class="badge badge-secondary"><?php echo strtoupper(str_replace('_', ' ', $error['context']['error_type'])); ?></span>

                                                        <?php if (isset($error['context']['troubleshooting_notes'])): ?>
                                                        <div class="alert alert-warning alert-sm mt-2">
                                                            <strong>Troubleshooting Notes:</strong>
                                                            <ul class="mb-0 mt-1">
                                                                <?php foreach ($error['context']['troubleshooting_notes'] as $note): ?>
                                                                <li><?php echo htmlspecialchars($note); ?></li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php endif; ?>

                                                    <hr>
                                                <?php endif; ?>

                                                <?php if (!empty($error['user_info'])): ?>
                                                <p><i class="fas fa-user"></i> <?php echo htmlspecialchars($error['user_info']); ?></p>
                                                <?php endif; ?>

                                                <?php if (!empty($error['request_info'])): ?>
                                                <p><i class="fas fa-globe"></i> <?php echo htmlspecialchars($error['request_info']); ?></p>
                                                <?php endif; ?>

                                                <?php if (!empty($error['details'])): ?>
                                                    <details class="mt-2">
                                                        <summary><strong>Raw Details</strong></summary>
                                                        <div class="mt-2">
                                                            <?php foreach ($error['details'] as $detail): ?>
                                                            <div><small><?php echo htmlspecialchars($detail); ?></small></div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    </details>
                                                <?php endif; ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>

                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                            <nav aria-label="Error pagination">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&category=<?php echo $selectedCategory; ?>&severity=<?php echo $selectedSeverity; ?>">Previous</a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&category=<?php echo $selectedCategory; ?>&severity=<?php echo $selectedSeverity; ?>"><?php echo $i; ?></a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&category=<?php echo $selectedCategory; ?>&severity=<?php echo $selectedSeverity; ?>">Next</a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Clear Logs Modal -->
    <div class="modal fade" id="clearLogsModal" tabindex="-1" role="dialog" aria-labelledby="clearLogsModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="clearLogsModalLabel">Clear Old Error Logs</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="days_to_keep">Keep logs from the last:</label>
                            <select name="days_to_keep" id="days_to_keep" class="form-control">
                                <option value="1">1 day</option>
                                <option value="3">3 days</option>
                                <option value="7" selected>7 days</option>
                                <option value="14">14 days</option>
                                <option value="30">30 days</option>
                            </select>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> This action will permanently delete older error logs and cannot be undone.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" name="clear_logs" class="btn btn-warning">Clear Logs</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
