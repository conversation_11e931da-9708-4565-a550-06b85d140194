<?php
/**
 * Test Enhanced Error Parsing
 * This script tests the improved error parsing functionality
 */

// Include the enhanced error handling system
require_once 'functions/enhanced-error-handler.php';
require_once 'functions/error-display.php';
require_once 'functions/error-logger.php';

echo "<h1>Enhanced Error Parsing Test</h1>";

// Test customer creation error similar to your issue
echo "<h2>Testing Customer Creation Error</h2>";

// Simulate the error you're experiencing
$customerData = [
    'username' => 'HI219',
    'email' => '<EMAIL>',
    'user_id' => 219
];

$apiResponse = [
    'success' => false,
    'appika_customer_id' => null,
    'message' => 'Appika API error: Customer No. HI219 already exist.'
];

// Log the error using enhanced error handler
$errorData = ErrorLogger::logCustomerError('Customer creation failed', $customerData, $apiResponse);

echo "<h3>Error Data Structure:</h3>";
echo "<pre>";
print_r($errorData);
echo "</pre>";

echo "<h3>Admin Error Display:</h3>";
echo ErrorDisplay::show($errorData, true, 'customer_creation');

echo "<h3>Customer Error Display:</h3>";
echo ErrorDisplay::show($errorData, false, 'customer_creation');

// Test parsing recent errors
echo "<h2>Testing Recent Error Parsing</h2>";

$recentErrors = EnhancedErrorHandler::getRecentErrors(null, 5);

echo "<h3>Recent Errors Found: " . count($recentErrors) . "</h3>";

foreach ($recentErrors as $index => $error) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>Error #" . ($index + 1) . "</h4>";
    echo "<p><strong>Timestamp:</strong> " . htmlspecialchars($error['timestamp']) . "</p>";
    echo "<p><strong>Severity:</strong> " . htmlspecialchars($error['severity']) . "</p>";
    echo "<p><strong>Category:</strong> " . htmlspecialchars($error['category']) . "</p>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($error['message']) . "</p>";
    
    if (!empty($error['context'])) {
        echo "<h5>Context Data:</h5>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px;'>";
        print_r($error['context']);
        echo "</pre>";
    }
    
    if (!empty($error['details'])) {
        echo "<h5>Details:</h5>";
        echo "<ul>";
        foreach ($error['details'] as $detail) {
            echo "<li>" . htmlspecialchars($detail) . "</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}

// Test specific API error scenarios
echo "<h2>Testing Different API Error Scenarios</h2>";

// Scenario 1: Server works, localhost doesn't
echo "<h3>Scenario 1: Environment Difference (Server vs Localhost)</h3>";
$envErrorData = ErrorLogger::logNetworkError(
    'Connection timeout to Appika API', 
    'https://dev-api-pooh-sgsg.appika.com/contact/customers',
    [
        'operation' => 'customer_creation',
        'environment' => 'localhost',
        'timeout_seconds' => 30,
        'customer_data' => $customerData
    ]
);
echo ErrorDisplay::show($envErrorData, true, 'customer_creation');

// Scenario 2: Duplicate customer error
echo "<h3>Scenario 2: Duplicate Customer Error</h3>";
$duplicateErrorData = ErrorLogger::logCustomerError(
    'Customer already exists in Appika',
    $customerData,
    [
        'status' => 400,
        'error' => 'Customer No. HI219 already exist.',
        'message' => 'Duplicate customer number detected'
    ]
);
echo ErrorDisplay::show($duplicateErrorData, true, 'customer_creation');

// Check log files
echo "<h2>Log Files Status</h2>";
$logFiles = [
    'api_errors.log',
    'database_errors.log',
    'validation_errors.log',
    'authentication_errors.log',
    'system_errors.log',
    'network_errors.log'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Log File</th><th>Exists</th><th>Size</th><th>Last Modified</th></tr>";

foreach ($logFiles as $logFile) {
    $filePath = 'logs/' . $logFile;
    $exists = file_exists($filePath);
    $size = $exists ? filesize($filePath) : 0;
    $lastModified = $exists ? date('Y-m-d H:i:s', filemtime($filePath)) : 'N/A';
    
    echo "<tr>";
    echo "<td>$logFile</td>";
    echo "<td>" . ($exists ? "✓ Yes" : "✗ No") . "</td>";
    echo "<td>" . ($exists ? number_format($size) . " bytes" : "0 bytes") . "</td>";
    echo "<td>$lastModified</td>";
    echo "</tr>";
}
echo "</table>";

echo "<hr>";
echo "<p><strong>Test completed!</strong> The enhanced error parsing should now show detailed context information.</p>";
echo "<p>Go to <a href='merlion/admin-error-dashboard.php'>Admin Error Dashboard</a> to see the improved error display with full context details.</p>";

?>

<!DOCTYPE html>
<html>
<head>
    <title>Enhanced Error Parsing Test</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .alert { margin: 10px 0; }
        h1, h2, h3 { color: #333; margin-top: 20px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <!-- Content is echoed above -->
</body>
</html>
