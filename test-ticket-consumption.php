<?php
/**
 * Test Ticket Consumption
 * Script to test and verify that tickets are consumed correctly (only once per ticket creation)
 */

require_once 'functions/server.php';
require_once 'functions/ticket-expiration-functions.php';

// Simple authentication check
session_start();
if (!isset($_SESSION['admin_username'])) {
    // For testing purposes, allow access. In production, add proper authentication
    // header('location: admin-login.php');
    // exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Consumption Test - HelloIT</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .code-block { background: #f8f9fa; padding: 10px; border-left: 3px solid #007bff; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mt-4 mb-4">Ticket Consumption Test</h1>
        
        <?php if (isset($_POST['test_user'])): ?>
            <?php
            $test_username = mysqli_real_escape_string($conn, $_POST['test_user']);
            
            echo "<h2>Testing Ticket Consumption for User: $test_username</h2>";
            
            // Get initial ticket counts
            $initial_query = "SELECT username, starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE username = '$test_username'";
            $initial_result = mysqli_query($conn, $initial_query);
            $initial_data = mysqli_fetch_assoc($initial_result);
            
            if (!$initial_data) {
                echo "<div class='test-fail'>❌ User not found: $test_username</div>";
            } else {
                echo "<div class='test-info'>";
                echo "<h4>Initial Ticket Counts:</h4>";
                echo "<ul>";
                echo "<li>Starter: {$initial_data['starter_tickets']}</li>";
                echo "<li>Premium: {$initial_data['premium_tickets']}</li>";
                echo "<li>Ultimate: {$initial_data['ultimate_tickets']}</li>";
                echo "</ul>";
                echo "</div>";
                
                // Get purchasetickets data
                $purchase_query = "SELECT ticket_type, SUM(remaining_tickets) as total_remaining 
                                  FROM purchasetickets 
                                  WHERE username = '$test_username' 
                                  AND remaining_tickets > 0 
                                  GROUP BY ticket_type";
                $purchase_result = mysqli_query($conn, $purchase_query);
                
                echo "<div class='test-info'>";
                echo "<h4>Purchase Tickets Data:</h4>";
                echo "<ul>";
                while ($row = mysqli_fetch_assoc($purchase_result)) {
                    echo "<li>{$row['ticket_type']}: {$row['total_remaining']} remaining</li>";
                }
                echo "</ul>";
                echo "</div>";
                
                // Test FIFO consumption
                if (isset($_POST['test_consume'])) {
                    $ticket_type = $_POST['ticket_type'];
                    
                    echo "<h3>Testing FIFO Consumption for $ticket_type tickets</h3>";
                    
                    // Test consuming 1 ticket
                    $consumption_result = useTicketsFIFO($test_username, $ticket_type, 1);
                    
                    if ($consumption_result) {
                        echo "<div class='test-pass'>✅ FIFO consumption successful</div>";
                    } else {
                        echo "<div class='test-fail'>❌ FIFO consumption failed</div>";
                    }
                    
                    // Get updated counts
                    $updated_result = mysqli_query($conn, $initial_query);
                    $updated_data = mysqli_fetch_assoc($updated_result);
                    
                    echo "<div class='test-info'>";
                    echo "<h4>Updated Ticket Counts:</h4>";
                    echo "<ul>";
                    echo "<li>Starter: {$updated_data['starter_tickets']} (was {$initial_data['starter_tickets']})</li>";
                    echo "<li>Premium: {$updated_data['premium_tickets']} (was {$initial_data['premium_tickets']})</li>";
                    echo "<li>Ultimate: {$updated_data['ultimate_tickets']} (was {$initial_data['ultimate_tickets']})</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                    // Check if consumption was correct
                    $expected_field = $ticket_type . '_tickets';
                    $expected_count = $initial_data[$expected_field] - 1;
                    $actual_count = $updated_data[$expected_field];
                    
                    if ($actual_count == $expected_count) {
                        echo "<div class='test-pass'>✅ Ticket consumption correct: $actual_count (expected $expected_count)</div>";
                    } else {
                        echo "<div class='test-fail'>❌ Ticket consumption incorrect: $actual_count (expected $expected_count)</div>";
                    }
                    
                    // Show purchase tickets after consumption
                    $purchase_result2 = mysqli_query($conn, $purchase_query);
                    echo "<div class='test-info'>";
                    echo "<h4>Purchase Tickets After Consumption:</h4>";
                    echo "<ul>";
                    while ($row = mysqli_fetch_assoc($purchase_result2)) {
                        echo "<li>{$row['ticket_type']}: {$row['total_remaining']} remaining</li>";
                    }
                    echo "</ul>";
                    echo "</div>";
                }
            }
            ?>
        <?php endif; ?>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>Test Ticket Consumption</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="test_user" class="form-label">Username to Test:</label>
                        <input type="text" class="form-control" id="test_user" name="test_user" 
                               value="<?php echo $_POST['test_user'] ?? ''; ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="ticket_type" class="form-label">Ticket Type to Test:</label>
                        <select class="form-control" id="ticket_type" name="ticket_type">
                            <option value="starter" <?php echo ($_POST['ticket_type'] ?? '') == 'starter' ? 'selected' : ''; ?>>Starter</option>
                            <option value="premium" <?php echo ($_POST['ticket_type'] ?? '') == 'premium' ? 'selected' : ''; ?>>Premium</option>
                            <option value="ultimate" <?php echo ($_POST['ticket_type'] ?? '') == 'ultimate' ? 'selected' : ''; ?>>Ultimate</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Check Current Status</button>
                    <button type="submit" name="test_consume" value="1" class="btn btn-warning">Test Consume 1 Ticket</button>
                </form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>Fix Applied</h3>
            </div>
            <div class="card-body">
                <div class="test-pass">
                    <h5>✅ Double Ticket Consumption Fix Applied</h5>
                    <p><strong>Problem:</strong> Tickets were being consumed twice when creating tickets on server (where Appika upload works)</p>
                    <p><strong>Root Cause:</strong> Both <code>useTicketsFIFO()</code> and <code>syncUserTableTickets()</code> were being called in sequence</p>
                    <p><strong>Solution:</strong> Removed redundant <code>syncUserTableTickets()</code> call from create-ticket.php</p>
                </div>
                
                <div class="code-block mt-3">
                    <strong>Changes Made:</strong><br>
                    1. Removed duplicate syncUserTableTickets() call in front-end/create-ticket.php<br>
                    2. Re-enabled syncUserTableTickets() within useTicketsFIFO() function<br>
                    3. Now tickets are consumed only once per ticket creation
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>Verification Steps</h3>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Test on Server:</strong> Create a ticket and verify only 1 ticket is consumed</li>
                    <li><strong>Test on Localhost:</strong> Create a ticket and verify only 1 ticket is consumed</li>
                    <li><strong>Check Logs:</strong> Monitor ticket_api.log for successful operations</li>
                    <li><strong>Database Check:</strong> Verify purchasetickets and user tables are in sync</li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <strong>Note:</strong> The fix ensures consistent behavior between server and localhost environments.
                    Tickets will now be consumed correctly regardless of whether Appika API upload succeeds or fails.
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="performance-dashboard.php" class="btn btn-secondary">← Back to Performance Dashboard</a>
            <a href="apply-performance-optimizations.php" class="btn btn-info">Run Full Optimizations</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
